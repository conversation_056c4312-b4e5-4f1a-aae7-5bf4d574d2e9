import React, { useEffect, useState } from "react";
import { 
  <PERSON>, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle,
  Button,
  Badge,
  useTheme 
} from "@nui/ui";
import { CheckCircle, XCircle, AlertCircle, RefreshCw } from "lucide-react";

interface TestResult {
  name: string;
  passed: boolean;
  message: string;
  details?: string;
}

export function ThemeTest() {
  const { config, isDark, availableVariants } = useTheme();
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [domState, setDomState] = useState<{
    classes: string[];
    dataTheme: string | null;
    cssVariables: Record<string, string>;
  }>({
    classes: [],
    dataTheme: null,
    cssVariables: {}
  });

  const runTests = () => {
    const results: TestResult[] = [];
    const root = document.documentElement;
    const computedStyle = getComputedStyle(root);

    // Test 1: Dark mode class application
    const hasDarkClass = root.classList.contains('dark');
    const expectedDarkClass = isDark;
    results.push({
      name: 'Dark Mode Class',
      passed: hasDarkClass === expectedDarkClass,
      message: `Expected: ${expectedDarkClass}, Actual: ${hasDarkClass}`,
      details: `The 'dark' class should ${expectedDarkClass ? 'be present' : 'not be present'} on the html element`
    });

    // Test 2: Theme variant data attribute
    const dataTheme = root.getAttribute('data-theme');
    const expectedDataTheme = config.variant !== 'default' ? config.variant : null;
    results.push({
      name: 'Theme Variant Attribute',
      passed: dataTheme === expectedDataTheme,
      message: `Expected: ${expectedDataTheme || 'none'}, Actual: ${dataTheme || 'none'}`,
      details: `The data-theme attribute should ${expectedDataTheme ? `be set to '${expectedDataTheme}'` : 'not be present'}`
    });

    // Test 3: CSS Variables existence
    const requiredVariables = [
      '--background',
      '--foreground',
      '--primary',
      '--primary-foreground',
      '--secondary',
      '--secondary-foreground',
      '--border',
      '--muted',
      '--muted-foreground'
    ];

    const definedVariables = requiredVariables.filter(variable => {
      const value = computedStyle.getPropertyValue(variable).trim();
      return !!value;
    });

    results.push({
      name: 'CSS Variables Definition',
      passed: definedVariables.length === requiredVariables.length,
      message: `${definedVariables.length}/${requiredVariables.length} variables defined`,
      details: `Missing: ${requiredVariables.filter(v => !definedVariables.includes(v)).join(', ') || 'none'}`
    });

    // Test 4: CSS Variables have valid OKLCH values
    const backgroundValue = computedStyle.getPropertyValue('--background').trim();
    const foregroundValue = computedStyle.getPropertyValue('--foreground').trim();
    const isValidOKLCH = (value: string) => value.includes('oklch') || value.includes('rgb') || value.includes('#');
    
    results.push({
      name: 'CSS Variables Format',
      passed: isValidOKLCH(backgroundValue) && isValidOKLCH(foregroundValue),
      message: `Background: ${backgroundValue.substring(0, 30)}..., Foreground: ${foregroundValue.substring(0, 30)}...`,
      details: 'CSS variables should contain valid color values (OKLCH, RGB, or hex)'
    });

    // Test 5: Theme consistency with React state
    results.push({
      name: 'React State Consistency',
      passed: true, // This is more informational
      message: `Mode: ${config.mode}, Variant: ${config.variant}, isDark: ${isDark}`,
      details: 'React theme state should match DOM application'
    });

    // Test 6: Available variants detection
    results.push({
      name: 'Available Variants Detection',
      passed: availableVariants.length > 0,
      message: `Found ${availableVariants.length} variants: ${availableVariants.join(', ')}`,
      details: 'Theme system should detect available theme variants from CSS'
    });

    setTestResults(results);
  };

  const updateDOMState = () => {
    const root = document.documentElement;
    const computedStyle = getComputedStyle(root);
    
    const cssVariables: Record<string, string> = {};
    const variablesToCheck = [
      '--background', '--foreground', '--primary', '--primary-foreground',
      '--secondary', '--secondary-foreground', '--border', '--muted', '--muted-foreground'
    ];
    
    variablesToCheck.forEach(variable => {
      cssVariables[variable] = computedStyle.getPropertyValue(variable).trim();
    });

    setDomState({
      classes: Array.from(root.classList),
      dataTheme: root.getAttribute('data-theme'),
      cssVariables
    });
  };

  useEffect(() => {
    runTests();
    updateDOMState();
  }, [config, isDark, availableVariants]);

  useEffect(() => {
    // Auto-refresh DOM state every 2 seconds
    const interval = setInterval(updateDOMState, 2000);
    return () => clearInterval(interval);
  }, []);

  const getTestIcon = (passed: boolean) => {
    if (passed) return <CheckCircle className="size-4 text-green-600" />;
    return <XCircle className="size-4 text-red-600" />;
  };

  const getTestBadge = (passed: boolean) => {
    return (
      <Badge variant={passed ? "success" : "danger"} className="ml-2">
        {passed ? "PASS" : "FAIL"}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <AlertCircle className="size-5" />
                Theme DOM Test
              </CardTitle>
              <CardDescription>
                Verify that themes are properly applied to the DOM
              </CardDescription>
            </div>
            <Button onClick={runTests} variant="outline" size="sm" className="gap-2">
              <RefreshCw className="size-4" />
              Refresh Tests
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Current DOM State */}
          <div className="p-4 rounded-lg border bg-card/30">
            <h4 className="font-medium mb-3">Current DOM State</h4>
            <div className="space-y-2 text-sm">
              <div>
                <strong>HTML Classes:</strong> {domState.classes.length ? domState.classes.join(', ') : 'none'}
              </div>
              <div>
                <strong>data-theme:</strong> {domState.dataTheme || 'none'}
              </div>
              <div>
                <strong>React State:</strong> mode={config.mode}, variant={config.variant}, isDark={isDark}
              </div>
            </div>
          </div>

          {/* Test Results */}
          <div className="space-y-3">
            <h4 className="font-medium">Test Results</h4>
            {testResults.map((result, index) => (
              <div key={index} className="p-3 rounded-lg border bg-card/30">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    {getTestIcon(result.passed)}
                    <span className="font-medium">{result.name}</span>
                    {getTestBadge(result.passed)}
                  </div>
                </div>
                <div className="text-sm text-muted-foreground mb-1">
                  {result.message}
                </div>
                {result.details && (
                  <div className="text-xs text-muted-foreground">
                    {result.details}
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* CSS Variables Preview */}
          <div className="p-4 rounded-lg border bg-card/30">
            <h4 className="font-medium mb-3">CSS Variables</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-xs font-mono">
              {Object.entries(domState.cssVariables).map(([variable, value]) => (
                <div key={variable} className="flex justify-between">
                  <span className="text-muted-foreground">{variable}:</span>
                  <span className="truncate ml-2" title={value}>
                    {value || 'undefined'}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
