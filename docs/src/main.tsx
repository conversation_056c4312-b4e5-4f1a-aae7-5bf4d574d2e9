import React from "react";
import React<PERSON><PERSON> from "react-dom/client";
import {
  Activity,
  AlertCircle,
  AlertTriangle,
  Award,
  BarChart3,
  Bell,
  Boxes,
  Building,
  CheckCircle,
  Clock,
  Download,
  Eye,
  FileText,
  Filter,
  Gauge,
  Grid3X3,
  Heart,
  Info,
  Layers,
  Layout,
  Mail,
  MessageSquare,
  Monitor,
  Moon,
  MousePointer,
  Palette,
  Plus,
  RotateCcw,
  Search,
  Settings,
  Shield,
  Shuffle,
  SortAsc,
  Sparkles,
  Star,
  Sun,
  Target,
  TrendingUp,
  Upload,
  User,
  Users,
  Zap,
} from "lucide-react";
import {
  Alert,
  AlertDescription,
  AlertTitle,
  Avatar,
  AvatarFallback,
  AvatarImage,
  Badge,
  Button,
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
  Checkbox,
  Header,
  HeaderBrand,
  HeaderNavLink,
  Input,
  Label,
  Progress,
  RadioGroup,
  RadioGroupItem,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Separator,
  Skeleton,
  Slider,
  Switch,
  Tabs,
  TabsContent,
  TabsList,
  <PERSON><PERSON><PERSON>rigger,
  Textarea,
  ThemeProvider,
  ThemeToggle,
  Toggle,
  useTheme,
} from "@nui/ui";

function App() {
  const {
    config: { mode, variant },
    isDark,
    availableVariants,
    setMode,
    setVariant,
    toggleMode,
    cycleVariant,
  } = useTheme();

  return (
    <>
      <div className="min-h-screen bg-background text-foreground">
        <Header
          title={
            <HeaderBrand>
              nui
              <Badge variant="success" className="ml-2">
                Live
              </Badge>
            </HeaderBrand>
          }
          navigation={
            <>
              <HeaderNavLink href="#" active>
                Demo
              </HeaderNavLink>
              <HeaderNavLink href="#">Components</HeaderNavLink>
              <HeaderNavLink href="#">Docs</HeaderNavLink>
            </>
          }
          actions={
            <div className="flex items-center gap-2">
              <Badge variant="outline">Admin</Badge>
              <Avatar size="sm">
                <AvatarImage
                  src="https://github.com/shadcn.png"
                  alt="User Avatar"
                />
                <AvatarFallback>CN</AvatarFallback>
              </Avatar>
            </div>
          }
          themeToggleProps={{
            showLabels: false,
          }}
          sticky
          bordered
        />

        <div className="max-w-6xl mx-auto p-8 space-y-8">
          <div className="text-center space-y-2">
            <h1 className="text-4xl font-bold">nui Component Library</h1>
            <p className="text-muted-foreground text-lg">
              Explore our comprehensive collection of UI components with live
              theme switching
            </p>
          </div>

          {/* Current Theme Status Bar */}
          <div className="flex items-center justify-center gap-4 p-4 bg-card/50 border rounded-lg">
            <div className="flex items-center gap-2 text-sm">
              <span className="text-muted-foreground">Mode:</span>
              <Badge variant={isDark ? "secondary" : "default"}>{mode}</Badge>
            </div>
            <Separator orientation="vertical" className="h-4" />
            <div className="flex items-center gap-2 text-sm">
              <span className="text-muted-foreground">Variant:</span>
              <Badge variant="outline">{variant}</Badge>
            </div>
            <Separator orientation="vertical" className="h-4" />
            <div className="flex items-center gap-2 text-sm">
              <span className="text-muted-foreground">Available:</span>
              <code className="text-xs bg-muted px-2 py-1 rounded">
                {availableVariants.join(", ")}
              </code>
            </div>
          </div>

          <Tabs defaultValue="theme" className="w-full">
            <TabsList className="grid w-full grid-cols-6">
              <TabsTrigger value="theme">Theme</TabsTrigger>
              <TabsTrigger value="buttons">Buttons</TabsTrigger>
              <TabsTrigger value="forms">Forms</TabsTrigger>
              <TabsTrigger value="feedback">Feedback</TabsTrigger>
              <TabsTrigger value="data">Data</TabsTrigger>
              <TabsTrigger value="layout">Layout</TabsTrigger>
            </TabsList>

            <TabsContent value="theme" className="space-y-6">
              {/* Hero Section */}
              <div className="relative overflow-hidden rounded-xl bg-gradient-to-br from-primary/10 via-background to-secondary/10 p-8 border">
                <div className="relative z-10">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="p-2 rounded-lg bg-primary/10">
                      <Palette className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold">Theme System</h2>
                      <p className="text-muted-foreground">
                        Customize the appearance and behavior of all components
                      </p>
                    </div>
                  </div>

                  {/* Quick Theme Controls */}
                  <div className="flex flex-wrap items-center gap-4 mt-6">
                    <div className="flex items-center gap-2">
                      <Sun className="h-4 w-4" />
                      <Switch
                        checked={isDark}
                        onCheckedChange={toggleMode}
                        className="data-[state=checked]:bg-primary"
                      />
                      <Moon className="h-4 w-4" />
                    </div>

                    <Separator orientation="vertical" className="h-6" />

                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium">Variant:</span>
                      <div className="flex gap-1">
                        {availableVariants.map((v) => (
                          <Button
                            key={v}
                            onClick={() => setVariant(v)}
                            variant={variant === v ? "default" : "ghost"}
                            size="sm"
                            className="h-8 px-3 text-xs"
                          >
                            {v.charAt(0).toUpperCase() + v.slice(1)}
                          </Button>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Background decoration */}
                <div className="absolute top-0 right-0 w-32 h-32 bg-primary/5 rounded-full blur-3xl" />
                <div className="absolute bottom-0 left-0 w-24 h-24 bg-secondary/5 rounded-full blur-2xl" />
              </div>

              {/* Main Controls Grid */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Theme Controls Card */}
                <Card className="relative overflow-hidden">
                  <CardHeader className="pb-4">
                    <div className="flex items-center gap-2">
                      <Settings className="h-5 w-5 text-primary" />
                      <CardTitle className="text-lg">Theme Controls</CardTitle>
                    </div>
                    <CardDescription>
                      Fine-tune your theme preferences
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* Mode Selection */}
                    <div className="space-y-3">
                      <Label className="text-sm font-semibold flex items-center gap-2">
                        <Monitor className="h-4 w-4" />
                        Display Mode
                      </Label>
                      <div className="grid grid-cols-3 gap-2 p-1 bg-muted rounded-lg">
                        {(["light", "dark", "auto"] as const).map((m) => (
                          <Button
                            key={m}
                            onClick={() => setMode(m)}
                            variant={mode === m ? "default" : "ghost"}
                            size="sm"
                            className="relative"
                          >
                            {m === "light" && <Sun className="h-4 w-4 mr-1" />}
                            {m === "dark" && <Moon className="h-4 w-4 mr-1" />}
                            {m === "auto" && (
                              <Monitor className="h-4 w-4 mr-1" />
                            )}
                            {m.charAt(0).toUpperCase() + m.slice(1)}
                          </Button>
                        ))}
                      </div>
                    </div>

                    {/* Variant Selection */}
                    <div className="space-y-3">
                      <Label className="text-sm font-semibold flex items-center gap-2">
                        <Palette className="h-4 w-4" />
                        Color Variant
                      </Label>
                      <Select
                        value={variant}
                        onValueChange={(value) => setVariant(value as string)}
                      >
                        <SelectTrigger className="w-full">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {availableVariants.map((v) => (
                            <SelectItem key={v} value={v}>
                              <div className="flex items-center gap-2">
                                <div
                                  className={`w-3 h-3 rounded-full bg-${v}-500`}
                                />
                                {v.charAt(0).toUpperCase() + v.slice(1)}
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Quick Actions */}
                    <div className="space-y-3">
                      <Label className="text-sm font-semibold">
                        Quick Actions
                      </Label>
                      <div className="grid grid-cols-2 gap-2">
                        <Button
                          onClick={toggleMode}
                          variant="outline"
                          size="sm"
                          className="justify-start"
                        >
                          <RotateCcw className="h-4 w-4 mr-2" />
                          Toggle Mode
                        </Button>
                        <Button
                          onClick={() => cycleVariant(availableVariants)}
                          variant="outline"
                          size="sm"
                          className="justify-start"
                        >
                          <Shuffle className="h-4 w-4 mr-2" />
                          Cycle Variant
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Theme Components Card */}
                <Card className="relative overflow-hidden">
                  <CardHeader className="pb-4">
                    <div className="flex items-center gap-2">
                      <Layers className="h-5 w-5 text-primary" />
                      <CardTitle className="text-lg">
                        Theme Components
                      </CardTitle>
                    </div>
                    <CardDescription>
                      Pre-built theme toggle components
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid gap-3">
                      <div className="flex items-center justify-between p-3 rounded-lg border bg-card/50">
                        <div className="space-y-1">
                          <span className="text-sm font-medium">
                            Mode Toggle
                          </span>
                          <p className="text-xs text-muted-foreground">
                            Light/Dark switcher
                          </p>
                        </div>
                        <ThemeToggle modeOnly />
                      </div>

                      <div className="flex items-center justify-between p-3 rounded-lg border bg-card/50">
                        <div className="space-y-1">
                          <span className="text-sm font-medium">
                            Variant Toggle
                          </span>
                          <p className="text-xs text-muted-foreground">
                            Color variant switcher
                          </p>
                        </div>
                        <ThemeToggle variantOnly />
                      </div>

                      <div className="flex items-center justify-between p-3 rounded-lg border bg-card/50">
                        <div className="space-y-1">
                          <span className="text-sm font-medium">
                            Combined Toggle
                          </span>
                          <p className="text-xs text-muted-foreground">
                            Full theme control
                          </p>
                        </div>
                        <ThemeToggle />
                      </div>

                      <div className="flex items-center justify-between p-3 rounded-lg border bg-card/50">
                        <div className="space-y-1">
                          <span className="text-sm font-medium">
                            With Labels
                          </span>
                          <p className="text-xs text-muted-foreground">
                            Descriptive labels
                          </p>
                        </div>
                        <ThemeToggle showLabels />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Live Preview Section */}
              <Card className="relative overflow-hidden">
                <CardHeader className="pb-4">
                  <div className="flex items-center gap-2">
                    <Eye className="h-5 w-5 text-primary" />
                    <CardTitle className="text-lg">Live Preview</CardTitle>
                  </div>
                  <CardDescription>
                    See how components adapt to your theme changes in real-time
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {/* Buttons Preview */}
                    <div className="space-y-3 p-4 rounded-lg border bg-card/30">
                      <h4 className="font-medium text-sm">Buttons</h4>
                      <div className="space-y-2">
                        <div className="flex gap-2">
                          <Button size="sm">Primary</Button>
                          <Button size="sm" variant="secondary">
                            Secondary
                          </Button>
                        </div>
                        <div className="flex gap-2">
                          <Button size="sm" variant="outline">
                            Outline
                          </Button>
                          <Button size="sm" variant="ghost">
                            Ghost
                          </Button>
                        </div>
                      </div>
                    </div>

                    {/* Form Elements Preview */}
                    <div className="space-y-3 p-4 rounded-lg border bg-card/30">
                      <h4 className="font-medium text-sm">Form Elements</h4>
                      <div className="space-y-3">
                        <Input placeholder="Sample input" className="h-8" />
                        <div className="flex items-center space-x-2">
                          <Checkbox id="preview-checkbox" />
                          <Label htmlFor="preview-checkbox" className="text-sm">
                            Checkbox
                          </Label>
                        </div>
                        <div className="flex items-center justify-between">
                          <Label className="text-sm">Switch</Label>
                          <Switch />
                        </div>
                      </div>
                    </div>

                    {/* Status Elements Preview */}
                    <div className="space-y-3 p-4 rounded-lg border bg-card/30">
                      <h4 className="font-medium text-sm">Status Elements</h4>
                      <div className="space-y-2">
                        <div className="flex flex-wrap gap-1">
                          <Badge variant="success" className="text-xs">
                            Success
                          </Badge>
                          <Badge variant="warning" className="text-xs">
                            Warning
                          </Badge>
                          <Badge variant="info" className="text-xs">
                            Info
                          </Badge>
                        </div>
                        <Alert className="py-2">
                          <AlertCircle className="h-4 w-4" />
                          <AlertDescription className="text-xs">
                            Sample alert message
                          </AlertDescription>
                        </Alert>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="buttons" className="space-y-6">
              {/* Hero Section */}
              <div className="relative overflow-hidden rounded-xl bg-gradient-to-br from-blue-500/10 via-background to-purple-500/10 p-8 border">
                <div className="relative z-10">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="p-2 rounded-lg bg-blue-500/10 border border-blue-500/20">
                      <MousePointer className="size-6 text-blue-600" />
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold">Buttons & Actions</h2>
                      <p className="text-muted-foreground">
                        Interactive elements that drive user engagement
                      </p>
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-3">
                    <Button className="gap-2">
                      <Zap className="size-4" />
                      Primary Action
                    </Button>
                    <Button variant="outline" className="gap-2">
                      <Target className="size-4" />
                      Secondary
                    </Button>
                    <Button variant="ghost" size="icon">
                      <Heart className="size-4" />
                    </Button>
                  </div>
                </div>

                {/* Decorative elements */}
                <div className="absolute top-4 right-4 opacity-20">
                  <div className="grid grid-cols-3 gap-1">
                    {Array.from({ length: 9 }).map((_, i) => (
                      <div
                        key={i}
                        className="w-2 h-2 rounded-full bg-blue-500"
                      />
                    ))}
                  </div>
                </div>
              </div>

              {/* Main Content Grid */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Button Variants Card */}
                <Card className="relative overflow-hidden">
                  <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 to-purple-500" />
                  <CardHeader>
                    <div className="flex items-center gap-2">
                      <Sparkles className="size-5 text-blue-600" />
                      <CardTitle>Button Variants</CardTitle>
                    </div>
                    <CardDescription>
                      Different styles for various use cases
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-4">
                      <div className="p-4 rounded-lg border bg-card/30">
                        <h4 className="font-medium text-sm mb-3 flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-blue-500" />
                          Primary Actions
                        </h4>
                        <div className="flex flex-wrap gap-2">
                          <Button variant="default">Default</Button>
                          <Button variant="secondary">Secondary</Button>
                          <Button variant="destructive">Destructive</Button>
                        </div>
                      </div>

                      <div className="p-4 rounded-lg border bg-card/30">
                        <h4 className="font-medium text-sm mb-3 flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-purple-500" />
                          Subtle Actions
                        </h4>
                        <div className="flex flex-wrap gap-2">
                          <Button variant="outline">Outline</Button>
                          <Button variant="ghost">Ghost</Button>
                          <Button variant="link">Link</Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Interactive Elements Card */}
                <Card className="relative overflow-hidden">
                  <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-purple-500 to-pink-500" />
                  <CardHeader>
                    <div className="flex items-center gap-2">
                      <Activity className="size-5 text-purple-600" />
                      <CardTitle>Interactive Elements</CardTitle>
                    </div>
                    <CardDescription>
                      Specialized button types and controls
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-4">
                      <div className="p-4 rounded-lg border bg-card/30">
                        <h4 className="font-medium text-sm mb-3 flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-green-500" />
                          Icon Buttons
                        </h4>
                        <div className="flex flex-wrap items-center gap-2">
                          <Button size="icon-sm" title="Small">
                            <Heart className="size-3" />
                          </Button>
                          <Button size="icon" title="Medium">
                            <Star className="size-4" />
                          </Button>
                          <Button size="icon-lg" title="Large">
                            <Settings className="size-5" />
                          </Button>
                        </div>
                      </div>

                      <div className="p-4 rounded-lg border bg-card/30">
                        <h4 className="font-medium text-sm mb-3 flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-orange-500" />
                          Toggle States
                        </h4>
                        <div className="flex flex-wrap gap-2">
                          <Toggle>
                            <Bell className="size-4" />
                            Notifications
                          </Toggle>
                          <Toggle variant="outline">
                            <Mail className="size-4" />
                            Messages
                          </Toggle>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Button Sizes Showcase */}
              <Card className="relative overflow-hidden">
                <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-green-500 to-blue-500" />
                <CardHeader>
                  <div className="flex items-center gap-2">
                    <Gauge className="size-5 text-green-600" />
                    <CardTitle>Size Variations</CardTitle>
                  </div>
                  <CardDescription>
                    Different button sizes for various contexts and hierarchies
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="text-center p-6 rounded-lg border bg-card/30">
                      <h4 className="font-medium mb-4 flex items-center justify-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-blue-500" />
                        Small
                      </h4>
                      <div className="space-y-3">
                        <Button size="sm" className="w-full">
                          Small Button
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="w-full gap-2"
                        >
                          <Plus className="size-3" />
                          Add Item
                        </Button>
                      </div>
                    </div>

                    <div className="text-center p-6 rounded-lg border bg-card/30">
                      <h4 className="font-medium mb-4 flex items-center justify-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-purple-500" />
                        Medium
                      </h4>
                      <div className="space-y-3">
                        <Button size="md" className="w-full">
                          Medium Button
                        </Button>
                        <Button
                          size="md"
                          variant="outline"
                          className="w-full gap-2"
                        >
                          <Download className="size-4" />
                          Download
                        </Button>
                      </div>
                    </div>

                    <div className="text-center p-6 rounded-lg border bg-card/30">
                      <h4 className="font-medium mb-4 flex items-center justify-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-green-500" />
                        Large
                      </h4>
                      <div className="space-y-3">
                        <Button size="lg" className="w-full">
                          Large Button
                        </Button>
                        <Button
                          size="lg"
                          variant="outline"
                          className="w-full gap-2"
                        >
                          <Upload className="size-5" />
                          Upload File
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="forms" className="space-y-6">
              {/* Hero Section */}
              <div className="relative overflow-hidden rounded-xl bg-gradient-to-br from-green-500/10 via-background to-blue-500/10 p-8 border">
                <div className="relative z-10">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="p-2 rounded-lg bg-green-500/10 border border-green-500/20">
                      <FileText className="size-6 text-green-600" />
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold">Form Controls</h2>
                      <p className="text-muted-foreground">
                        Comprehensive input elements for data collection
                      </p>
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-3">
                    <Input
                      placeholder="Quick demo input"
                      className="max-w-xs"
                      leadingIcon={<Search />}
                    />
                    <Button className="gap-2">
                      <Plus className="size-4" />
                      Add Field
                    </Button>
                  </div>
                </div>

                {/* Decorative elements */}
                <div className="absolute top-4 right-4 opacity-20">
                  <Grid3X3 className="size-12 text-green-500" />
                </div>
              </div>

              {/* Main Content Grid */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Text Inputs Card */}
                <Card className="relative overflow-hidden">
                  <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-green-500 to-blue-500" />
                  <CardHeader>
                    <div className="flex items-center gap-2">
                      <MessageSquare className="size-5 text-green-600" />
                      <CardTitle>Text Inputs</CardTitle>
                    </div>
                    <CardDescription>
                      Various input types for text data collection
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-4">
                      <div className="space-y-3">
                        <Label
                          htmlFor="name"
                          className="flex items-center gap-2"
                        >
                          <User className="size-4" />
                          Full Name
                        </Label>
                        <Input
                          id="name"
                          placeholder="Enter your full name"
                          leadingIcon={<User />}
                        />
                      </div>

                      <div className="space-y-3">
                        <Label
                          htmlFor="email"
                          className="flex items-center gap-2"
                        >
                          <Mail className="size-4" />
                          Email Address
                        </Label>
                        <Input
                          id="email"
                          type="email"
                          placeholder="<EMAIL>"
                          leadingIcon={<Mail />}
                        />
                      </div>

                      <div className="space-y-3">
                        <Label
                          htmlFor="search"
                          className="flex items-center gap-2"
                        >
                          <Search className="size-4" />
                          Search
                        </Label>
                        <Input
                          id="search"
                          placeholder="Search components..."
                          leadingIcon={<Search />}
                        />
                      </div>

                      <div className="space-y-3">
                        <Label
                          htmlFor="message"
                          className="flex items-center gap-2"
                        >
                          <MessageSquare className="size-4" />
                          Message
                        </Label>
                        <Textarea
                          id="message"
                          placeholder="Type your message here..."
                          rows={3}
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Selection Controls Card */}
                <Card className="relative overflow-hidden">
                  <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 to-purple-500" />
                  <CardHeader>
                    <div className="flex items-center gap-2">
                      <Filter className="size-5 text-blue-600" />
                      <CardTitle>Selection Controls</CardTitle>
                    </div>
                    <CardDescription>
                      Dropdowns, checkboxes, and radio buttons
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-4">
                      <div className="space-y-3">
                        <Label className="flex items-center gap-2">
                          <SortAsc className="size-4" />
                          Priority Level
                        </Label>
                        <Select defaultValue="medium">
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="low">Low Priority</SelectItem>
                            <SelectItem value="medium">
                              Medium Priority
                            </SelectItem>
                            <SelectItem value="high">High Priority</SelectItem>
                            <SelectItem value="urgent">Urgent</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-3">
                        <Label className="flex items-center gap-2">
                          <Bell className="size-4" />
                          Notification Preferences
                        </Label>
                        <div className="space-y-3 p-4 rounded-lg border bg-card/30">
                          <div className="flex items-center space-x-2">
                            <Checkbox id="notifications" />
                            <Label htmlFor="notifications" className="text-sm">
                              Email notifications
                            </Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Checkbox id="marketing" />
                            <Label htmlFor="marketing" className="text-sm">
                              Marketing emails
                            </Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Checkbox id="updates" defaultChecked />
                            <Label htmlFor="updates" className="text-sm">
                              Product updates
                            </Label>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-3">
                        <Label className="flex items-center gap-2">
                          <Building className="size-4" />
                          Account Type
                        </Label>
                        <RadioGroup
                          defaultValue="personal"
                          className="p-4 rounded-lg border bg-card/30"
                        >
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="personal" id="personal" />
                            <Label htmlFor="personal" className="text-sm">
                              Personal Account
                            </Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="business" id="business" />
                            <Label htmlFor="business" className="text-sm">
                              Business Account
                            </Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem
                              value="enterprise"
                              id="enterprise"
                            />
                            <Label htmlFor="enterprise" className="text-sm">
                              Enterprise Account
                            </Label>
                          </div>
                        </RadioGroup>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Interactive Controls */}
              <Card className="relative overflow-hidden">
                <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-purple-500 to-pink-500" />
                <CardHeader>
                  <div className="flex items-center gap-2">
                    <Activity className="size-5 text-purple-600" />
                    <CardTitle>Interactive Controls</CardTitle>
                  </div>
                  <CardDescription>
                    Sliders, switches, and other interactive form elements
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div className="space-y-6">
                      <div className="p-6 rounded-lg border bg-card/30">
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <Label className="flex items-center gap-2">
                              <Gauge className="size-4" />
                              Volume Level
                            </Label>
                            <span className="text-sm font-medium text-muted-foreground">
                              75%
                            </span>
                          </div>
                          <Slider defaultValue={[75]} max={100} step={1} />
                        </div>
                      </div>

                      <div className="p-6 rounded-lg border bg-card/30">
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <Label className="flex items-center gap-2">
                              <Activity className="size-4" />
                              Performance
                            </Label>
                            <span className="text-sm font-medium text-muted-foreground">
                              45%
                            </span>
                          </div>
                          <Slider defaultValue={[45]} max={100} step={5} />
                        </div>
                      </div>
                    </div>

                    <div className="space-y-6">
                      <div className="p-6 rounded-lg border bg-card/30">
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <Label
                              htmlFor="dark-mode"
                              className="flex items-center gap-2"
                            >
                              <Moon className="size-4" />
                              Dark Mode
                            </Label>
                            <Switch id="dark-mode" />
                          </div>

                          <div className="flex items-center justify-between">
                            <Label
                              htmlFor="notifications-switch"
                              className="flex items-center gap-2"
                            >
                              <Bell className="size-4" />
                              Notifications
                            </Label>
                            <Switch id="notifications-switch" defaultChecked />
                          </div>

                          <div className="flex items-center justify-between">
                            <Label
                              htmlFor="auto-save"
                              className="flex items-center gap-2"
                            >
                              <Shield className="size-4" />
                              Auto-save
                            </Label>
                            <Switch id="auto-save" />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="feedback" className="space-y-6">
              {/* Hero Section */}
              <div className="relative overflow-hidden rounded-xl bg-gradient-to-br from-orange-500/10 via-background to-red-500/10 p-8 border">
                <div className="relative z-10">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="p-2 rounded-lg bg-orange-500/10 border border-orange-500/20">
                      <Award className="size-6 text-orange-600" />
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold">Feedback & Status</h2>
                      <p className="text-muted-foreground">
                        Visual feedback components for user communication
                      </p>
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-3">
                    <Badge variant="success" className="gap-1">
                      <CheckCircle className="size-3" />
                      Success
                    </Badge>
                    <Badge variant="warning" className="gap-1">
                      <AlertTriangle className="size-3" />
                      Warning
                    </Badge>
                    <Badge variant="danger" className="gap-1">
                      <AlertCircle className="size-3" />
                      Error
                    </Badge>
                  </div>
                </div>

                {/* Decorative elements */}
                <div className="absolute top-4 right-4 opacity-20">
                  <TrendingUp className="size-12 text-orange-500" />
                </div>
              </div>

              {/* Main Content Grid */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Status Indicators Card */}
                <Card className="relative overflow-hidden">
                  <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-orange-500 to-red-500" />
                  <CardHeader>
                    <div className="flex items-center gap-2">
                      <Award className="size-5 text-orange-600" />
                      <CardTitle>Status Indicators</CardTitle>
                    </div>
                    <CardDescription>
                      Badges and labels for status communication
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-4">
                      <div className="p-4 rounded-lg border bg-card/30">
                        <h4 className="font-medium text-sm mb-3 flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-blue-500" />
                          Basic Badges
                        </h4>
                        <div className="flex flex-wrap gap-2">
                          <Badge variant="default">Default</Badge>
                          <Badge variant="secondary">Secondary</Badge>
                          <Badge variant="outline">Outline</Badge>
                        </div>
                      </div>

                      <div className="p-4 rounded-lg border bg-card/30">
                        <h4 className="font-medium text-sm mb-3 flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-green-500" />
                          Status Badges
                        </h4>
                        <div className="flex flex-wrap gap-2">
                          <Badge variant="success" className="gap-1">
                            <CheckCircle className="size-3" />
                            Success
                          </Badge>
                          <Badge variant="warning" className="gap-1">
                            <AlertTriangle className="size-3" />
                            Warning
                          </Badge>
                          <Badge variant="info" className="gap-1">
                            <Info className="size-3" />
                            Info
                          </Badge>
                          <Badge variant="danger" className="gap-1">
                            <AlertCircle className="size-3" />
                            Danger
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Progress & Loading Card */}
                <Card className="relative overflow-hidden">
                  <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 to-purple-500" />
                  <CardHeader>
                    <div className="flex items-center gap-2">
                      <Clock className="size-5 text-blue-600" />
                      <CardTitle>Progress & Loading</CardTitle>
                    </div>
                    <CardDescription>
                      Progress bars and loading states
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-4">
                      <div className="p-4 rounded-lg border bg-card/30">
                        <h4 className="font-medium text-sm mb-4 flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-blue-500" />
                          Progress Indicators
                        </h4>
                        <div className="space-y-4">
                          <div>
                            <div className="flex justify-between text-sm mb-2">
                              <span className="flex items-center gap-2">
                                <Upload className="size-3" />
                                Upload Progress
                              </span>
                              <span className="font-medium">45%</span>
                            </div>
                            <Progress value={45} />
                          </div>

                          <div>
                            <div className="flex justify-between text-sm mb-2">
                              <span className="flex items-center gap-2">
                                <Download className="size-3" />
                                Installation
                              </span>
                              <span className="font-medium">78%</span>
                            </div>
                            <Progress value={78} />
                          </div>
                        </div>
                      </div>

                      <div className="p-4 rounded-lg border bg-card/30">
                        <h4 className="font-medium text-sm mb-4 flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-purple-500" />
                          Loading States
                        </h4>
                        <div className="space-y-2">
                          <Skeleton className="h-4 w-full" />
                          <Skeleton className="h-4 w-3/4" />
                          <Skeleton className="h-4 w-1/2" />
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Alert Messages */}
              <Card className="relative overflow-hidden">
                <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-green-500 to-orange-500" />
                <CardHeader>
                  <div className="flex items-center gap-2">
                    <MessageSquare className="size-5 text-green-600" />
                    <CardTitle>Alert Messages</CardTitle>
                  </div>
                  <CardDescription>
                    Contextual alerts for different message types and scenarios
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <Alert>
                        <Info className="size-4" />
                        <AlertTitle>Information</AlertTitle>
                        <AlertDescription>
                          This is an informational alert with helpful details
                          about the current state.
                        </AlertDescription>
                      </Alert>

                      <Alert variant="success">
                        <CheckCircle className="size-4" />
                        <AlertTitle>Success</AlertTitle>
                        <AlertDescription>
                          Your changes have been saved successfully and are now
                          live.
                        </AlertDescription>
                      </Alert>
                    </div>

                    <div className="space-y-4">
                      <Alert variant="warning">
                        <AlertTriangle className="size-4" />
                        <AlertTitle>Warning</AlertTitle>
                        <AlertDescription>
                          Please review your settings carefully before
                          proceeding with this action.
                        </AlertDescription>
                      </Alert>

                      <Alert variant="danger">
                        <AlertCircle className="size-4" />
                        <AlertTitle>Error</AlertTitle>
                        <AlertDescription>
                          Something went wrong. Please check your input and try
                          again.
                        </AlertDescription>
                      </Alert>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="data" className="space-y-6">
              {/* Hero Section */}
              <div className="relative overflow-hidden rounded-xl bg-gradient-to-br from-purple-500/10 via-background to-blue-500/10 p-8 border">
                <div className="relative z-10">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="p-2 rounded-lg bg-purple-500/10 border border-purple-500/20">
                      <BarChart3 className="size-6 text-purple-600" />
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold">Data Display</h2>
                      <p className="text-muted-foreground">
                        Rich components for presenting data and user information
                      </p>
                    </div>
                  </div>

                  <div className="flex flex-wrap items-center gap-3">
                    <div className="flex items-center gap-2">
                      <Avatar size="sm">
                        <AvatarImage
                          src="https://github.com/shadcn.png"
                          alt="@shadcn"
                        />
                        <AvatarFallback>CN</AvatarFallback>
                      </Avatar>
                      <span className="text-sm font-medium">John Doe</span>
                    </div>
                    <Badge variant="outline" className="gap-1">
                      <TrendingUp className="size-3" />
                      +23% Growth
                    </Badge>
                  </div>
                </div>

                {/* Decorative elements */}
                <div className="absolute top-4 right-4 opacity-20">
                  <Users className="size-12 text-purple-500" />
                </div>
              </div>

              {/* Main Content Grid */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* User Profiles Card */}
                <Card className="relative overflow-hidden">
                  <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-purple-500 to-blue-500" />
                  <CardHeader>
                    <div className="flex items-center gap-2">
                      <Users className="size-5 text-purple-600" />
                      <CardTitle>User Profiles</CardTitle>
                    </div>
                    <CardDescription>
                      Avatar components and user representations
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-4">
                      <div className="p-4 rounded-lg border bg-card/30">
                        <h4 className="font-medium text-sm mb-3 flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-blue-500" />
                          Avatar Sizes
                        </h4>
                        <div className="flex items-center gap-4">
                          <div className="text-center">
                            <Avatar size="sm">
                              <AvatarImage
                                src="https://github.com/shadcn.png"
                                alt="@shadcn"
                              />
                              <AvatarFallback>SM</AvatarFallback>
                            </Avatar>
                            <p className="text-xs text-muted-foreground mt-1">
                              Small
                            </p>
                          </div>
                          <div className="text-center">
                            <Avatar>
                              <AvatarImage
                                src="https://github.com/shadcn.png"
                                alt="@shadcn"
                              />
                              <AvatarFallback>MD</AvatarFallback>
                            </Avatar>
                            <p className="text-xs text-muted-foreground mt-1">
                              Medium
                            </p>
                          </div>
                          <div className="text-center">
                            <Avatar size="lg">
                              <AvatarImage
                                src="https://github.com/shadcn.png"
                                alt="@shadcn"
                              />
                              <AvatarFallback>LG</AvatarFallback>
                            </Avatar>
                            <p className="text-xs text-muted-foreground mt-1">
                              Large
                            </p>
                          </div>
                        </div>
                      </div>

                      <div className="p-4 rounded-lg border bg-card/30">
                        <h4 className="font-medium text-sm mb-3 flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-green-500" />
                          Profile Cards
                        </h4>
                        <div className="space-y-3">
                          <div className="flex items-center gap-3 p-3 rounded-lg border bg-background/50">
                            <Avatar size="sm">
                              <AvatarImage
                                src="https://github.com/shadcn.png"
                                alt="John Doe"
                              />
                              <AvatarFallback>JD</AvatarFallback>
                            </Avatar>
                            <div className="flex-1">
                              <div className="font-medium text-sm">
                                John Doe
                              </div>
                              <div className="text-xs text-muted-foreground">
                                <EMAIL>
                              </div>
                            </div>
                            <Badge variant="success" className="text-xs">
                              Active
                            </Badge>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Statistics Card */}
                <Card className="relative overflow-hidden">
                  <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 to-green-500" />
                  <CardHeader>
                    <div className="flex items-center gap-2">
                      <TrendingUp className="size-5 text-blue-600" />
                      <CardTitle>Key Metrics</CardTitle>
                    </div>
                    <CardDescription>
                      Important statistics and performance indicators
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 gap-4">
                      <Card className="border bg-gradient-to-br from-blue-500/5 to-blue-500/10">
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-sm font-medium text-muted-foreground flex items-center gap-2">
                                <Users className="size-4" />
                                Total Users
                              </p>
                              <p className="text-2xl font-bold text-blue-600">
                                1,234
                              </p>
                              <p className="text-xs text-muted-foreground mt-1">
                                +12% from last month
                              </p>
                            </div>
                            <div className="p-2 rounded-full bg-blue-500/10">
                              <Users className="size-6 text-blue-600" />
                            </div>
                          </div>
                        </CardContent>
                      </Card>

                      <Card className="border bg-gradient-to-br from-green-500/5 to-green-500/10">
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-sm font-medium text-muted-foreground flex items-center gap-2">
                                <Mail className="size-4" />
                                Messages
                              </p>
                              <p className="text-2xl font-bold text-green-600">
                                567
                              </p>
                              <p className="text-xs text-muted-foreground mt-1">
                                +8% from last week
                              </p>
                            </div>
                            <div className="p-2 rounded-full bg-green-500/10">
                              <Mail className="size-6 text-green-600" />
                            </div>
                          </div>
                        </CardContent>
                      </Card>

                      <Card className="border bg-gradient-to-br from-orange-500/5 to-orange-500/10">
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-sm font-medium text-muted-foreground flex items-center gap-2">
                                <Bell className="size-4" />
                                Notifications
                              </p>
                              <p className="text-2xl font-bold text-orange-600">
                                89
                              </p>
                              <p className="text-xs text-muted-foreground mt-1">
                                +3% from yesterday
                              </p>
                            </div>
                            <div className="p-2 rounded-full bg-orange-500/10">
                              <Bell className="size-6 text-orange-600" />
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="layout" className="space-y-6">
              {/* Hero Section */}
              <div className="relative overflow-hidden rounded-xl bg-gradient-to-br from-indigo-500/10 via-background to-cyan-500/10 p-8 border">
                <div className="relative z-10">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="p-2 rounded-lg bg-indigo-500/10 border border-indigo-500/20">
                      <Layout className="size-6 text-indigo-600" />
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold">Layout Components</h2>
                      <p className="text-muted-foreground">
                        Structural elements for organizing and presenting
                        content
                      </p>
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-3">
                    <Card className="p-3 bg-background/50 border">
                      <div className="flex items-center gap-2">
                        <Grid3X3 className="size-4 text-indigo-600" />
                        <span className="text-sm font-medium">Grid System</span>
                      </div>
                    </Card>
                    <Card className="p-3 bg-background/50 border">
                      <div className="flex items-center gap-2">
                        <Boxes className="size-4 text-cyan-600" />
                        <span className="text-sm font-medium">
                          Card Layouts
                        </span>
                      </div>
                    </Card>
                  </div>
                </div>

                {/* Decorative elements */}
                <div className="absolute top-4 right-4 opacity-20">
                  <Boxes className="size-12 text-indigo-500" />
                </div>
              </div>

              {/* Main Content Grid */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Card Structures */}
                <Card className="relative overflow-hidden">
                  <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-indigo-500 to-purple-500" />
                  <CardHeader>
                    <div className="flex items-center gap-2">
                      <Boxes className="size-5 text-indigo-600" />
                      <CardTitle>Card Structures</CardTitle>
                    </div>
                    <CardDescription>
                      Flexible card layouts for content organization
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="p-4 rounded-lg border bg-card/30">
                      <h4 className="font-medium text-sm mb-3 flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-blue-500" />
                        Project Card
                      </h4>
                      <Card className="border bg-background/50">
                        <CardHeader className="pb-3">
                          <CardTitle className="text-sm">
                            Project Alpha
                          </CardTitle>
                          <CardDescription className="text-xs">
                            Revolutionary new project
                          </CardDescription>
                        </CardHeader>
                        <CardContent className="pt-0 pb-3">
                          <p className="text-xs text-muted-foreground">
                            Transform how we think about user interfaces.
                          </p>
                        </CardContent>
                        <CardFooter className="pt-0">
                          <Button size="sm" className="h-6 text-xs w-full">
                            Learn More
                          </Button>
                        </CardFooter>
                      </Card>
                    </div>

                    <div className="p-4 rounded-lg border bg-card/30">
                      <h4 className="font-medium text-sm mb-3 flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-green-500" />
                        Analytics Card
                      </h4>
                      <Card className="border bg-background/50">
                        <CardHeader className="pb-3">
                          <CardTitle className="text-sm">Dashboard</CardTitle>
                          <CardDescription className="text-xs">
                            Real-time insights
                          </CardDescription>
                        </CardHeader>
                        <CardContent className="pt-0 pb-3">
                          <div className="space-y-2">
                            <div className="flex justify-between text-xs">
                              <span>Conversion</span>
                              <span className="font-medium">12.5%</span>
                            </div>
                            <Progress value={12.5} className="h-1" />
                          </div>
                        </CardContent>
                        <CardFooter className="pt-0">
                          <Button
                            size="sm"
                            variant="outline"
                            className="h-6 text-xs w-full"
                          >
                            View Details
                          </Button>
                        </CardFooter>
                      </Card>
                    </div>
                  </CardContent>
                </Card>

                {/* Layout Utilities */}
                <Card className="relative overflow-hidden">
                  <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-purple-500 to-cyan-500" />
                  <CardHeader>
                    <div className="flex items-center gap-2">
                      <Grid3X3 className="size-5 text-purple-600" />
                      <CardTitle>Layout Utilities</CardTitle>
                    </div>
                    <CardDescription>
                      Essential tools for content separation and organization
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-4">
                      <div className="p-4 rounded-lg border bg-card/30">
                        <h4 className="font-medium text-sm mb-3 flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-blue-500" />
                          Separators
                        </h4>
                        <div className="space-y-3">
                          <div>
                            <p className="text-xs text-muted-foreground mb-2">
                              Horizontal
                            </p>
                            <Separator />
                          </div>
                          <div className="flex items-center gap-3">
                            <p className="text-xs text-muted-foreground">
                              Vertical
                            </p>
                            <Separator orientation="vertical" className="h-4" />
                            <p className="text-xs text-muted-foreground">
                              Divider
                            </p>
                          </div>
                        </div>
                      </div>

                      <div className="p-4 rounded-lg border bg-card/30">
                        <h4 className="font-medium text-sm mb-3 flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-purple-500" />
                          Settings Layout
                        </h4>
                        <Card className="border bg-background/50">
                          <CardContent className="p-4">
                            <div className="space-y-3">
                              <div className="flex items-center justify-between">
                                <h5 className="text-sm font-medium">
                                  Team Settings
                                </h5>
                                <Badge variant="info" className="text-xs">
                                  New
                                </Badge>
                              </div>
                              <Separator />
                              <div className="flex items-center justify-between">
                                <div>
                                  <p className="text-xs font-medium">
                                    Notifications
                                  </p>
                                  <p className="text-xs text-muted-foreground">
                                    Get team updates
                                  </p>
                                </div>
                                <Switch defaultChecked />
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Design Principles */}
              <Card className="relative overflow-hidden">
                <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-cyan-500 to-indigo-500" />
                <CardHeader>
                  <div className="flex items-center gap-2">
                    <Award className="size-5 text-cyan-600" />
                    <CardTitle>Design Principles</CardTitle>
                  </div>
                  <CardDescription>
                    Core principles that guide our layout system design and
                    implementation
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <Card className="border bg-gradient-to-br from-indigo-500/5 to-indigo-500/10 text-center">
                      <CardContent className="p-6">
                        <div className="w-12 h-12 bg-indigo-500/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                          <Layout className="size-6 text-indigo-600" />
                        </div>
                        <h3 className="font-semibold mb-2 flex items-center justify-center gap-2">
                          <Sparkles className="size-4" />
                          Flexible
                        </h3>
                        <p className="text-sm text-muted-foreground">
                          Responsive layouts that gracefully adapt to any screen
                          size and device
                        </p>
                      </CardContent>
                    </Card>

                    <Card className="border bg-gradient-to-br from-purple-500/5 to-purple-500/10 text-center">
                      <CardContent className="p-6">
                        <div className="w-12 h-12 bg-purple-500/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                          <Boxes className="size-6 text-purple-600" />
                        </div>
                        <h3 className="font-semibold mb-2 flex items-center justify-center gap-2">
                          <Target className="size-4" />
                          Modular
                        </h3>
                        <p className="text-sm text-muted-foreground">
                          Composable components that can be combined to build
                          complex interfaces
                        </p>
                      </CardContent>
                    </Card>

                    <Card className="border bg-gradient-to-br from-cyan-500/5 to-cyan-500/10 text-center">
                      <CardContent className="p-6">
                        <div className="w-12 h-12 bg-cyan-500/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                          <Grid3X3 className="size-6 text-cyan-600" />
                        </div>
                        <h3 className="font-semibold mb-2 flex items-center justify-center gap-2">
                          <Shield className="size-4" />
                          Structured
                        </h3>
                        <p className="text-sm text-muted-foreground">
                          Well-organized content with clear visual hierarchy and
                          consistent spacing
                        </p>
                      </CardContent>
                    </Card>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </>
  );
}

ReactDOM.createRoot(document.getElementById("root")!).render(
  <React.StrictMode>
    <ThemeProvider>
      <App />
    </ThemeProvider>
  </React.StrictMode>,
);
