<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Theme DOM Test</title>
    <style>
        body {
            font-family: system-ui, -apple-system, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ccc;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .test-result {
            margin: 10px 0;
            padding: 8px;
            border-radius: 4px;
        }
        .pass {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .fail {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .theme-preview {
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
            background: var(--background, #fff);
            color: var(--foreground, #000);
            border: 1px solid var(--border, #ccc);
        }
        .color-swatch {
            display: inline-block;
            width: 20px;
            height: 20px;
            border-radius: 4px;
            margin-right: 8px;
            vertical-align: middle;
            border: 1px solid #ccc;
        }
        button {
            margin: 5px;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background: var(--primary, #007bff);
            color: var(--primary-foreground, #fff);
        }
        .controls {
            margin: 20px 0;
            padding: 15px;
            background: #f0f0f0;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <h1>Theme DOM Application Test</h1>
    <p>This page tests if themes are being properly applied to the DOM by checking HTML attributes, CSS classes, and CSS variables.</p>

    <div class="controls">
        <h3>Theme Controls</h3>
        <button onclick="applyTheme('light', 'default')">Light + Default</button>
        <button onclick="applyTheme('dark', 'default')">Dark + Default</button>
        <button onclick="applyTheme('light', 'neutral')">Light + Neutral</button>
        <button onclick="applyTheme('dark', 'neutral')">Dark + Neutral</button>
        <button onclick="applyTheme('light', 'slate')">Light + Slate</button>
        <button onclick="applyTheme('dark', 'slate')">Dark + Slate</button>
        <button onclick="runAllTests()">Run All Tests</button>
    </div>

    <div class="test-section">
        <h3>Current DOM State</h3>
        <div id="dom-state"></div>
    </div>

    <div class="test-section">
        <h3>CSS Variables Test</h3>
        <div id="css-variables"></div>
    </div>

    <div class="test-section">
        <h3>Theme Preview</h3>
        <div class="theme-preview">
            <h4>Sample Content</h4>
            <p>This content uses CSS variables for theming. Background: var(--background), Foreground: var(--foreground)</p>
            <button>Primary Button</button>
            <div style="background: var(--secondary, #6c757d); color: var(--secondary-foreground, #fff); padding: 8px; border-radius: 4px; margin: 8px 0;">
                Secondary Background
            </div>
        </div>
    </div>

    <div class="test-section">
        <h3>Test Results</h3>
        <div id="test-results"></div>
    </div>

    <script>
        // Theme application function (simulates the theme-dom.ts logic)
        function applyTheme(mode, variant) {
            const root = document.documentElement;
            
            // Clear existing theme attributes and classes
            root.removeAttribute('data-theme');
            root.classList.remove('dark');
            
            // Apply dark mode class
            if (mode === 'dark') {
                root.classList.add('dark');
            }
            
            // Apply theme variant as data attribute
            if (variant && variant !== 'default') {
                root.setAttribute('data-theme', variant);
            }
            
            updateDOMState();
            runAllTests();
        }

        // Update DOM state display
        function updateDOMState() {
            const root = document.documentElement;
            const domState = document.getElementById('dom-state');
            
            const classes = Array.from(root.classList);
            const dataTheme = root.getAttribute('data-theme');
            
            domState.innerHTML = `
                <div class="info">
                    <strong>HTML Element Classes:</strong> ${classes.length ? classes.join(', ') : 'none'}<br>
                    <strong>data-theme attribute:</strong> ${dataTheme || 'none'}<br>
                    <strong>Computed dark mode:</strong> ${root.classList.contains('dark') ? 'Yes' : 'No'}
                </div>
            `;
        }

        // Test CSS variables
        function testCSSVariables() {
            const root = document.documentElement;
            const computedStyle = getComputedStyle(root);
            
            const variables = [
                '--background',
                '--foreground', 
                '--primary',
                '--primary-foreground',
                '--secondary',
                '--secondary-foreground',
                '--border',
                '--muted',
                '--muted-foreground'
            ];
            
            const results = variables.map(variable => {
                const value = computedStyle.getPropertyValue(variable).trim();
                return {
                    variable,
                    value,
                    defined: !!value
                };
            });
            
            const cssVariablesDiv = document.getElementById('css-variables');
            cssVariablesDiv.innerHTML = results.map(result => `
                <div class="${result.defined ? 'pass' : 'fail'}">
                    <strong>${result.variable}:</strong> ${result.value || 'undefined'}
                    ${result.defined ? '✓' : '✗'}
                </div>
            `).join('');
            
            return results;
        }

        // Run comprehensive tests
        function runAllTests() {
            const results = [];
            const root = document.documentElement;
            
            // Test 1: Check if dark class is properly applied
            const hasDarkClass = root.classList.contains('dark');
            results.push({
                test: 'Dark mode class application',
                passed: true, // We can't test this without knowing expected state
                message: `Dark class ${hasDarkClass ? 'is' : 'is not'} present`
            });
            
            // Test 2: Check if data-theme attribute is applied
            const dataTheme = root.getAttribute('data-theme');
            results.push({
                test: 'Theme variant data attribute',
                passed: true, // We can't test this without knowing expected state
                message: `data-theme attribute: ${dataTheme || 'not set'}`
            });
            
            // Test 3: Check CSS variables
            const cssVarResults = testCSSVariables();
            const definedVars = cssVarResults.filter(r => r.defined).length;
            results.push({
                test: 'CSS variables definition',
                passed: definedVars >= 5, // At least 5 variables should be defined
                message: `${definedVars}/${cssVarResults.length} CSS variables are defined`
            });
            
            // Test 4: Check if CSS variables change with theme
            const backgroundVar = getComputedStyle(root).getPropertyValue('--background').trim();
            const foregroundVar = getComputedStyle(root).getPropertyValue('--foreground').trim();
            results.push({
                test: 'CSS variables have values',
                passed: !!backgroundVar && !!foregroundVar,
                message: `Background: ${backgroundVar}, Foreground: ${foregroundVar}`
            });
            
            // Test 5: Check if transitions are disabled initially
            const hasNoTransition = root.classList.contains('no-transition');
            results.push({
                test: 'Transition control',
                passed: true, // This is informational
                message: `no-transition class ${hasNoTransition ? 'is' : 'is not'} present`
            });
            
            // Display results
            const testResultsDiv = document.getElementById('test-results');
            testResultsDiv.innerHTML = results.map(result => `
                <div class="${result.passed ? 'pass' : 'fail'}">
                    <strong>${result.test}:</strong> ${result.message} ${result.passed ? '✓' : '✗'}
                </div>
            `).join('');
            
            return results;
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateDOMState();
            runAllTests();
        });

        // Auto-refresh every 2 seconds to catch any changes
        setInterval(() => {
            updateDOMState();
        }, 2000);
    </script>
</body>
</html>
