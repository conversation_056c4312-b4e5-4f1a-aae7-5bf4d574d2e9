// Theme Utilities Test Script
// Run this in the browser console to test theme utilities

console.log('🎨 Testing Theme Utilities...\n');

// Test 1: DOM Utility - Check current DOM state
console.log('1️⃣ Testing DOM Utility:');
const root = document.documentElement;
console.log('Current HTML classes:', Array.from(root.classList));
console.log('Current data-theme:', root.getAttribute('data-theme'));
console.log('Has dark class:', root.classList.contains('dark'));

// Test 2: CSS Variables - Check if theme variables are applied
console.log('\n2️⃣ Testing CSS Variables:');
const computedStyle = getComputedStyle(root);
const themeVariables = [
  '--background',
  '--foreground', 
  '--primary',
  '--primary-foreground',
  '--secondary',
  '--border',
  '--muted'
];

themeVariables.forEach(variable => {
  const value = computedStyle.getPropertyValue(variable).trim();
  console.log(`${variable}: ${value || 'undefined'}`);
});

// Test 3: Persistence Utility - Check localStorage
console.log('\n3️⃣ Testing Persistence Utility:');
const themeKey = 'theme';
const storedTheme = localStorage.getItem(themeKey);
console.log('Stored theme in localStorage:', storedTheme);

if (storedTheme) {
  try {
    const parsed = JSON.parse(storedTheme);
    console.log('Parsed theme config:', parsed);
  } catch (e) {
    console.log('Error parsing stored theme:', e);
  }
}

// Test 4: Variants Detection - Check available variants
console.log('\n4️⃣ Testing Variants Detection:');

// Function to detect variants from stylesheets (similar to theme-variants.ts)
function detectVariantsFromCSS() {
  const foundVariants = new Set(['default']);
  const dataThemeRegex = /\[data-theme="([a-zA-Z0-9-_]+)"\]/g;
  
  for (const sheet of Array.from(document.styleSheets)) {
    try {
      // Skip cross-origin stylesheets
      if (sheet.href && !sheet.href.startsWith(window.location.origin)) {
        continue;
      }
      
      const rules = sheet.cssRules;
      if (!rules) continue;
      
      for (const rule of rules) {
        if (rule instanceof CSSStyleRule) {
          let match;
          while ((match = dataThemeRegex.exec(rule.selectorText))) {
            const variant = match[1];
            if (/^[a-zA-Z0-9-_]+$/.test(variant)) {
              foundVariants.add(variant);
            }
          }
        }
      }
    } catch (e) {
      // Skip inaccessible stylesheets
      console.log('Skipped stylesheet:', sheet.href || 'inline');
    }
  }
  
  return Array.from(foundVariants).sort();
}

const detectedVariants = detectVariantsFromCSS();
console.log('Detected theme variants:', detectedVariants);

// Test 5: Manual Theme Application
console.log('\n5️⃣ Testing Manual Theme Application:');

// Function to manually apply theme (similar to theme-dom.ts)
function testApplyTheme(mode, variant) {
  console.log(`Applying theme: mode=${mode}, variant=${variant}`);
  
  // Clear existing
  root.removeAttribute('data-theme');
  root.classList.remove('dark');
  
  // Apply variant
  if (variant && variant !== 'default') {
    root.setAttribute('data-theme', variant);
  }
  
  // Apply mode
  if (mode === 'dark') {
    root.classList.add('dark');
  }
  
  console.log('Applied - Classes:', Array.from(root.classList));
  console.log('Applied - data-theme:', root.getAttribute('data-theme'));
}

// Test different theme combinations
console.log('\n🧪 Testing theme combinations:');

// Test light + default
testApplyTheme('light', 'default');
setTimeout(() => {
  // Test dark + neutral
  testApplyTheme('dark', 'neutral');
  setTimeout(() => {
    // Test light + slate
    testApplyTheme('light', 'slate');
    setTimeout(() => {
      // Reset to original
      console.log('\n🔄 Resetting to original theme...');
      // You can restore the original theme here if needed
    }, 1000);
  }, 1000);
}, 1000);

// Test 6: Storage Operations
console.log('\n6️⃣ Testing Storage Operations:');

// Test storing theme
const testConfig = { mode: 'dark', variant: 'slate' };
console.log('Storing test config:', testConfig);
localStorage.setItem('test-theme', JSON.stringify(testConfig));

// Test retrieving theme
const retrieved = localStorage.getItem('test-theme');
console.log('Retrieved config:', retrieved);

// Test parsing
try {
  const parsed = JSON.parse(retrieved);
  console.log('Parsed successfully:', parsed);
} catch (e) {
  console.log('Parse error:', e);
}

// Cleanup test storage
localStorage.removeItem('test-theme');

// Test 7: System Preference Detection
console.log('\n7️⃣ Testing System Preference Detection:');
const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
console.log('System prefers dark mode:', prefersDark);

// Test media query listener
const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
console.log('Media query object:', mediaQuery);

// Summary
console.log('\n📊 Test Summary:');
console.log('✅ DOM utility: Can read/write classes and attributes');
console.log('✅ CSS variables: Available and readable');
console.log('✅ Persistence: localStorage read/write works');
console.log('✅ Variants detection: Found', detectedVariants.length, 'variants');
console.log('✅ System preferences: Detection works');

console.log('\n🎉 Theme utilities test completed!');
console.log('Check the browser visually to see theme changes applied.');
