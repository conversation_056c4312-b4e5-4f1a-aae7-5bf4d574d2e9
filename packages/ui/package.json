{"name": "@nui/ui", "version": "0.1.0", "description": "nui React components library", "main": "src/index.ts", "scripts": {"build": "tsup --config ../../.config/tsup.config.ts", "typecheck": "tsc --noEmit", "prepack": "bun run build && clean-package", "postpack": "clean-package restore"}, "peerDependencies": {"react": "^19.1.0", "react-dom": "^19.1.0"}, "dependencies": {"@base-ui-components/react": "1.0.0-beta.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk-base": "^0.0.4", "embla-carousel-react": "^8.6.0", "frimousse": "^0.2.0", "input-otp": "^1.4.2", "lucide-react": "^0.525.0", "react-day-picker": "^9.7.0", "react-hook-form": "^7.59.0", "recharts": "^3.0.2", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "vaul-base": "^0.0.5"}, "devDependencies": {"@types/react": "^19.1.8", "@types/react-dom": "^19.1.6"}, "clean-package": "../../.config/clean-package.json"}