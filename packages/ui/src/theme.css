@import "tailwindcss";

/* Base theme variables */
@theme {
  /* Core semantic colors */
  --background: oklch(0.985 0 0);
  --foreground: oklch(0.141 0.005 285.823);
  --border: oklch(0.871 0.006 286.286);
  --input: oklch(0.967 0.001 286.375);
  --ring: oklch(0.92 0.004 286.32);

  /* Primary colors */
  --primary: oklch(0.141 0.005 285.823);
  --primary-foreground: oklch(0.985 0 0);

  /* Secondary colors */
  --secondary: oklch(0.92 0.004 286.32);
  --secondary-foreground: oklch(0.37 0.013 285.805);

  /* Muted colors */
  --muted: oklch(0.944 0.0025 286.348);
  --muted-foreground: oklch(0.442 0.017 285.786);

  /* Accent colors */
  --accent: oklch(0.944 0.0025 286.348);
  --accent-foreground: oklch(0.141 0.005 285.823);

  /* Status colors */
  --destructive: oklch(0.541 0.229 27.422);
  --destructive-foreground: oklch(0.985 0 0);
  --danger: oklch(0.954 0.023 17.549);
  --danger-foreground: oklch(0.444 0.177 26.899);
  --danger-border: oklch(0.444 0.177 26.899);
  --warning: oklch(0.98 0.049 102.703);
  --warning-foreground: oklch(0.554 0.135 66.442);
  --warning-border: oklch(0.554 0.135 66.442);
  --info: oklch(0.951 0.023 255.095);
  --info-foreground: oklch(0.488 0.243 264.376);
  --info-border: oklch(0.488 0.243 264.376);
  --success: oklch(0.962 0.044 156.743);
  --success-foreground: oklch(0.448 0.119 151.328);
  --success-border: oklch(0.448 0.119 151.328);

  /* Surface colors */
  --card: oklch(0.976 0.0005 143.188);
  --card-foreground: oklch(0.141 0.005 285.823);
  --popover: oklch(0.976 0.0005 143.188);
  --popover-foreground: oklch(0.141 0.005 285.823);

  /* Chart colors */
  --chart-1: oklch(0.585 0.23 261.348);
  --chart-2: oklch(0.675 0.207 149.396);
  --chart-3: oklch(0.527 0.277 302.123);
  --chart-4: oklch(0.676 0.218 44.36);
  --chart-5: oklch(0.541 0.229 27.422);

  /* Border radius */
  --radius: 0.5rem;
}

/* Dark mode overrides */
.dark {
  --background: oklch(0.141 0.005 285.823);
  --foreground: oklch(0.985 0 0);
  --border: oklch(0.322 0.0095 285.919);
  --input: oklch(0.21 0.006 285.885);
  --ring: oklch(0.406 0.015 285.796);
  --primary: oklch(0.985 0 0);
  --primary-foreground: oklch(0.141 0.005 285.823);
  --secondary: oklch(0.21 0.006 285.885);
  --secondary-foreground: oklch(0.629 0.0155 286.003);
  --muted: oklch(0.242 0.006 285.959);
  --muted-foreground: oklch(0.552 0.016 285.938);
  --accent: oklch(0.242 0.006 285.959);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.985 0 0);
  --danger: oklch(0.22 0.08 26.1);
  --danger-foreground: oklch(0.671 0.214 23.774);
  --danger-border: oklch(0.671 0.214 23.774);
  --warning: oklch(0.24 0.056 52.0);
  --warning-foreground: oklch(0.738 0.173 80.941);
  --warning-border: oklch(0.738 0.173 80.941);
  --info: oklch(0.24 0.08 268.5);
  --info-foreground: oklch(0.665 0.1895 257.22);
  --info-border: oklch(0.665 0.1895 257.22);
  --success: oklch(0.23 0.055 153.0);
  --success-foreground: oklch(0.627 0.194 149.214);
  --success-border: oklch(0.627 0.194 149.214);
  --card: oklch(0.176 0.0055 285.854);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.176 0.0055 285.854);
  --popover-foreground: oklch(0.985 0 0);
  --chart-1: oklch(0.585 0.23 261.348);
  --chart-2: oklch(0.577 0.174 149.642);
  --chart-3: oklch(0.593 0.277 303.111);
  --chart-4: oklch(0.676 0.218 44.36);
  --chart-5: oklch(0.577 0.245 27.325);
}

/* Theme variant: Neutral */
[data-theme="neutral"] {
  --background: oklch(0.985 0 0);
  --foreground: oklch(0.141 0 0);
  --border: oklch(0.871 0 0);
  --input: oklch(0.967 0 0);
  --ring: oklch(0.92 0 0);
  --primary: oklch(0.141 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.92 0 0);
  --secondary-foreground: oklch(0.37 0 0);
  --muted: oklch(0.944 0 0);
  --muted-foreground: oklch(0.442 0 0);
  --accent: oklch(0.944 0 0);
  --accent-foreground: oklch(0.141 0 0);
  --destructive: oklch(0.541 0.229 27.422);
  --destructive-foreground: oklch(0.985 0 0);
  --danger: oklch(0.954 0.023 17.549);
  --danger-foreground: oklch(0.444 0.177 26.899);
  --danger-border: oklch(0.444 0.177 26.899);
  --warning: oklch(0.98 0.049 102.703);
  --warning-foreground: oklch(0.554 0.135 66.442);
  --warning-border: oklch(0.554 0.135 66.442);
  --info: oklch(0.951 0.023 255.095);
  --info-foreground: oklch(0.488 0.243 264.376);
  --info-border: oklch(0.488 0.243 264.376);
  --success: oklch(0.962 0.044 156.743);
  --success-foreground: oklch(0.448 0.119 151.328);
  --success-border: oklch(0.448 0.119 151.328);
  --card: oklch(0.976 0 0);
  --card-foreground: oklch(0.141 0 0);
  --popover: oklch(0.976 0 0);
  --popover-foreground: oklch(0.141 0 0);
  --chart-1: oklch(0.585 0.23 261.348);
  --chart-2: oklch(0.675 0.207 149.396);
  --chart-3: oklch(0.527 0.277 302.123);
  --chart-4: oklch(0.676 0.218 44.36);
  --chart-5: oklch(0.541 0.229 27.422);
}

[data-theme="neutral"].dark {
  --background: oklch(0.141 0 0);
  --foreground: oklch(0.985 0 0);
  --border: oklch(0.322 0 0);
  --input: oklch(0.21 0 0);
  --ring: oklch(0.406 0 0);
  --primary: oklch(0.985 0 0);
  --primary-foreground: oklch(0.141 0 0);
  --secondary: oklch(0.21 0 0);
  --secondary-foreground: oklch(0.629 0 0);
  --muted: oklch(0.242 0 0);
  --muted-foreground: oklch(0.552 0 0);
  --accent: oklch(0.242 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.985 0 0);
  --danger: oklch(0.22 0.08 26.1);
  --danger-foreground: oklch(0.671 0.214 23.774);
  --danger-border: oklch(0.671 0.214 23.774);
  --warning: oklch(0.24 0.056 52.0);
  --warning-foreground: oklch(0.738 0.173 80.941);
  --warning-border: oklch(0.738 0.173 80.941);
  --info: oklch(0.24 0.08 268.5);
  --info-foreground: oklch(0.665 0.1895 257.22);
  --info-border: oklch(0.665 0.1895 257.22);
  --success: oklch(0.23 0.055 153.0);
  --success-foreground: oklch(0.627 0.194 149.214);
  --success-border: oklch(0.627 0.194 149.214);
  --card: oklch(0.176 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.176 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --chart-1: oklch(0.585 0.23 261.348);
  --chart-2: oklch(0.577 0.174 149.642);
  --chart-3: oklch(0.593 0.277 303.111);
  --chart-4: oklch(0.676 0.218 44.36);
  --chart-5: oklch(0.577 0.245 27.325);
}

/* Theme variant: Gray */
[data-theme="gray"] {
  --background: oklch(0.985 0.002 220);
  --foreground: oklch(0.141 0.008 220);
  --border: oklch(0.871 0.008 220);
  --input: oklch(0.967 0.003 220);
  --ring: oklch(0.92 0.006 220);
  --primary: oklch(0.141 0.008 220);
  --primary-foreground: oklch(0.985 0.002 220);
  --secondary: oklch(0.92 0.006 220);
  --secondary-foreground: oklch(0.37 0.015 220);
  --muted: oklch(0.944 0.004 220);
  --muted-foreground: oklch(0.442 0.019 220);
  --accent: oklch(0.944 0.004 220);
  --accent-foreground: oklch(0.141 0.008 220);
  --destructive: oklch(0.541 0.229 27.422);
  --destructive-foreground: oklch(0.985 0 0);
  --danger: oklch(0.954 0.023 17.549);
  --danger-foreground: oklch(0.444 0.177 26.899);
  --danger-border: oklch(0.444 0.177 26.899);
  --warning: oklch(0.98 0.049 102.703);
  --warning-foreground: oklch(0.554 0.135 66.442);
  --warning-border: oklch(0.554 0.135 66.442);
  --info: oklch(0.951 0.023 255.095);
  --info-foreground: oklch(0.488 0.243 264.376);
  --info-border: oklch(0.488 0.243 264.376);
  --success: oklch(0.962 0.044 156.743);
  --success-foreground: oklch(0.448 0.119 151.328);
  --success-border: oklch(0.448 0.119 151.328);
  --card: oklch(0.976 0.001 220);
  --card-foreground: oklch(0.141 0.008 220);
  --popover: oklch(0.976 0.001 220);
  --popover-foreground: oklch(0.141 0.008 220);
  --chart-1: oklch(0.585 0.23 261.348);
  --chart-2: oklch(0.675 0.207 149.396);
  --chart-3: oklch(0.527 0.277 302.123);
  --chart-4: oklch(0.676 0.218 44.36);
  --chart-5: oklch(0.541 0.229 27.422);
}

[data-theme="gray"].dark {
  --background: oklch(0.141 0.008 220);
  --foreground: oklch(0.985 0.002 220);
  --border: oklch(0.322 0.012 220);
  --input: oklch(0.21 0.008 220);
  --ring: oklch(0.406 0.018 220);
  --primary: oklch(0.985 0.002 220);
  --primary-foreground: oklch(0.141 0.008 220);
  --secondary: oklch(0.21 0.008 220);
  --secondary-foreground: oklch(0.629 0.018 220);
  --muted: oklch(0.242 0.008 220);
  --muted-foreground: oklch(0.552 0.018 220);
  --accent: oklch(0.242 0.008 220);
  --accent-foreground: oklch(0.985 0.002 220);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.985 0 0);
  --danger: oklch(0.22 0.08 26.1);
  --danger-foreground: oklch(0.671 0.214 23.774);
  --danger-border: oklch(0.671 0.214 23.774);
  --warning: oklch(0.24 0.056 52.0);
  --warning-foreground: oklch(0.738 0.173 80.941);
  --warning-border: oklch(0.738 0.173 80.941);
  --info: oklch(0.24 0.08 268.5);
  --info-foreground: oklch(0.665 0.1895 257.22);
  --info-border: oklch(0.665 0.1895 257.22);
  --success: oklch(0.23 0.055 153.0);
  --success-foreground: oklch(0.627 0.194 149.214);
  --success-border: oklch(0.627 0.194 149.214);
  --card: oklch(0.176 0.007 220);
  --card-foreground: oklch(0.985 0.002 220);
  --popover: oklch(0.176 0.007 220);
  --popover-foreground: oklch(0.985 0.002 220);
  --chart-1: oklch(0.585 0.23 261.348);
  --chart-2: oklch(0.577 0.174 149.642);
  --chart-3: oklch(0.593 0.277 303.111);
  --chart-4: oklch(0.676 0.218 44.36);
  --chart-5: oklch(0.577 0.245 27.325);
}

/* Theme variant: Slate */
[data-theme="slate"] {
  --background: oklch(0.984 0.003 247.858);
  --foreground: oklch(0.129 0.042 264.695);
  --border: oklch(0.869 0.022 252.894);
  --input: oklch(0.968 0.007 247.896);
  --ring: oklch(0.929 0.013 255.508);
  --primary: oklch(0.129 0.042 264.695);
  --primary-foreground: oklch(0.984 0.003 247.858);
  --secondary: oklch(0.929 0.013 255.508);
  --secondary-foreground: oklch(0.372 0.044 257.287);
  --muted: oklch(0.949 0.01 251.702);
  --muted-foreground: oklch(0.446 0.043 257.281);
  --accent: oklch(0.949 0.01 251.702);
  --accent-foreground: oklch(0.129 0.042 264.695);
  --destructive: oklch(0.541 0.229 27.422);
  --destructive-foreground: oklch(0.985 0 0);
  --danger: oklch(0.954 0.023 17.549);
  --danger-foreground: oklch(0.444 0.177 26.899);
  --danger-border: oklch(0.444 0.177 26.899);
  --warning: oklch(0.98 0.049 102.703);
  --warning-foreground: oklch(0.554 0.135 66.442);
  --warning-border: oklch(0.554 0.135 66.442);
  --info: oklch(0.951 0.023 255.095);
  --info-foreground: oklch(0.488 0.243 264.376);
  --info-border: oklch(0.488 0.243 264.376);
  --success: oklch(0.962 0.044 156.743);
  --success-foreground: oklch(0.448 0.119 151.328);
  --success-border: oklch(0.448 0.119 151.328);
  --card: oklch(0.984 0.003 247.858);
  --card-foreground: oklch(0.129 0.042 264.695);
  --popover: oklch(0.984 0.003 247.858);
  --popover-foreground: oklch(0.129 0.042 264.695);
  --chart-1: oklch(0.585 0.23 261.348);
  --chart-2: oklch(0.675 0.207 149.396);
  --chart-3: oklch(0.527 0.277 302.123);
  --chart-4: oklch(0.676 0.218 44.36);
  --chart-5: oklch(0.541 0.229 27.422);
}

[data-theme="slate"].dark {
  --background: oklch(0.129 0.042 264.695);
  --foreground: oklch(0.984 0.003 247.858);
  --border: oklch(0.326 0.0425 258.659);
  --input: oklch(0.208 0.042 265.755);
  --ring: oklch(0.409 0.0435 257.284);
  --primary: oklch(0.984 0.003 247.858);
  --primary-foreground: oklch(0.129 0.042 264.695);
  --secondary: oklch(0.208 0.042 265.755);
  --secondary-foreground: oklch(0.629 0.043 257.103);
  --muted: oklch(0.244 0.0415 262.893);
  --muted-foreground: oklch(0.554 0.046 257.417);
  --accent: oklch(0.244 0.0415 262.893);
  --accent-foreground: oklch(0.984 0.003 247.858);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.985 0 0);
  --danger: oklch(0.22 0.08 26.1);
  --danger-foreground: oklch(0.671 0.214 23.774);
  --danger-border: oklch(0.671 0.214 23.774);
  --warning: oklch(0.24 0.056 52.0);
  --warning-foreground: oklch(0.738 0.173 80.941);
  --warning-border: oklch(0.738 0.173 80.941);
  --info: oklch(0.24 0.08 268.5);
  --info-foreground: oklch(0.665 0.1895 257.22);
  --info-border: oklch(0.665 0.1895 257.22);
  --success: oklch(0.23 0.055 153.0);
  --success-foreground: oklch(0.627 0.194 149.214);
  --success-border: oklch(0.627 0.194 149.214);
  --card: oklch(0.129 0.042 264.695);
  --card-foreground: oklch(0.984 0.003 247.858);
  --popover: oklch(0.129 0.042 264.695);
  --popover-foreground: oklch(0.984 0.003 247.858);
  --chart-1: oklch(0.585 0.23 261.348);
  --chart-2: oklch(0.577 0.174 149.642);
  --chart-3: oklch(0.593 0.277 303.111);
  --chart-4: oklch(0.676 0.218 44.36);
  --chart-5: oklch(0.577 0.245 27.325);
}

/* Theme variant: Stone */
[data-theme="stone"] {
  --background: oklch(0.985 0.001 60);
  --foreground: oklch(0.141 0.006 60);
  --border: oklch(0.871 0.006 60);
  --input: oklch(0.967 0.002 60);
  --ring: oklch(0.92 0.004 60);
  --primary: oklch(0.141 0.006 60);
  --primary-foreground: oklch(0.985 0.001 60);
  --secondary: oklch(0.92 0.004 60);
  --secondary-foreground: oklch(0.37 0.012 60);
  --muted: oklch(0.944 0.003 60);
  --muted-foreground: oklch(0.442 0.016 60);
  --accent: oklch(0.944 0.003 60);
  --accent-foreground: oklch(0.141 0.006 60);
  --destructive: oklch(0.541 0.229 27.422);
  --destructive-foreground: oklch(0.985 0 0);
  --danger: oklch(0.954 0.023 17.549);
  --danger-foreground: oklch(0.444 0.177 26.899);
  --danger-border: oklch(0.444 0.177 26.899);
  --warning: oklch(0.98 0.049 102.703);
  --warning-foreground: oklch(0.554 0.135 66.442);
  --warning-border: oklch(0.554 0.135 66.442);
  --info: oklch(0.951 0.023 255.095);
  --info-foreground: oklch(0.488 0.243 264.376);
  --info-border: oklch(0.488 0.243 264.376);
  --success: oklch(0.962 0.044 156.743);
  --success-foreground: oklch(0.448 0.119 151.328);
  --success-border: oklch(0.448 0.119 151.328);
  --card: oklch(0.976 0.0008 60);
  --card-foreground: oklch(0.141 0.006 60);
  --popover: oklch(0.976 0.0008 60);
  --popover-foreground: oklch(0.141 0.006 60);
  --chart-1: oklch(0.585 0.23 261.348);
  --chart-2: oklch(0.675 0.207 149.396);
  --chart-3: oklch(0.527 0.277 302.123);
  --chart-4: oklch(0.676 0.218 44.36);
  --chart-5: oklch(0.541 0.229 27.422);
}

[data-theme="stone"].dark {
  --background: oklch(0.141 0.006 60);
  --foreground: oklch(0.985 0.001 60);
  --border: oklch(0.322 0.009 60);
  --input: oklch(0.21 0.006 60);
  --ring: oklch(0.406 0.015 60);
  --primary: oklch(0.985 0.001 60);
  --primary-foreground: oklch(0.141 0.006 60);
  --secondary: oklch(0.21 0.006 60);
  --secondary-foreground: oklch(0.629 0.015 60);
  --muted: oklch(0.242 0.006 60);
  --muted-foreground: oklch(0.552 0.016 60);
  --accent: oklch(0.242 0.006 60);
  --accent-foreground: oklch(0.985 0.001 60);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.985 0 0);
  --danger: oklch(0.22 0.08 26.1);
  --danger-foreground: oklch(0.671 0.214 23.774);
  --danger-border: oklch(0.671 0.214 23.774);
  --warning: oklch(0.24 0.056 52.0);
  --warning-foreground: oklch(0.738 0.173 80.941);
  --warning-border: oklch(0.738 0.173 80.941);
  --info: oklch(0.24 0.08 268.5);
  --info-foreground: oklch(0.665 0.1895 257.22);
  --info-border: oklch(0.665 0.1895 257.22);
  --success: oklch(0.23 0.055 153.0);
  --success-foreground: oklch(0.627 0.194 149.214);
  --success-border: oklch(0.627 0.194 149.214);
  --card: oklch(0.176 0.005 60);
  --card-foreground: oklch(0.985 0.001 60);
  --popover: oklch(0.176 0.005 60);
  --popover-foreground: oklch(0.985 0.001 60);
  --chart-1: oklch(0.585 0.23 261.348);
  --chart-2: oklch(0.577 0.174 149.642);
  --chart-3: oklch(0.593 0.277 303.111);
  --chart-4: oklch(0.676 0.218 44.36);
  --chart-5: oklch(0.577 0.245 27.325);
}

/* Theme variant: Zinc */
[data-theme="zinc"] {
  --background: oklch(0.985 0.001 240);
  --foreground: oklch(0.141 0.005 240);
  --border: oklch(0.871 0.005 240);
  --input: oklch(0.967 0.002 240);
  --ring: oklch(0.92 0.003 240);
  --primary: oklch(0.141 0.005 240);
  --primary-foreground: oklch(0.985 0.001 240);
  --secondary: oklch(0.92 0.003 240);
  --secondary-foreground: oklch(0.37 0.011 240);
  --muted: oklch(0.944 0.002 240);
  --muted-foreground: oklch(0.442 0.015 240);
  --accent: oklch(0.944 0.002 240);
  --accent-foreground: oklch(0.141 0.005 240);
  --destructive: oklch(0.541 0.229 27.422);
  --destructive-foreground: oklch(0.985 0 0);
  --danger: oklch(0.954 0.023 17.549);
  --danger-foreground: oklch(0.444 0.177 26.899);
  --danger-border: oklch(0.444 0.177 26.899);
  --warning: oklch(0.98 0.049 102.703);
  --warning-foreground: oklch(0.554 0.135 66.442);
  --warning-border: oklch(0.554 0.135 66.442);
  --info: oklch(0.951 0.023 255.095);
  --info-foreground: oklch(0.488 0.243 264.376);
  --info-border: oklch(0.488 0.243 264.376);
  --success: oklch(0.962 0.044 156.743);
  --success-foreground: oklch(0.448 0.119 151.328);
  --success-border: oklch(0.448 0.119 151.328);
  --card: oklch(0.976 0.0007 240);
  --card-foreground: oklch(0.141 0.005 240);
  --popover: oklch(0.976 0.0007 240);
  --popover-foreground: oklch(0.141 0.005 240);
  --chart-1: oklch(0.585 0.23 261.348);
  --chart-2: oklch(0.675 0.207 149.396);
  --chart-3: oklch(0.527 0.277 302.123);
  --chart-4: oklch(0.676 0.218 44.36);
  --chart-5: oklch(0.541 0.229 27.422);
}

[data-theme="zinc"].dark {
  --background: oklch(0.141 0.005 240);
  --foreground: oklch(0.985 0.001 240);
  --border: oklch(0.322 0.008 240);
  --input: oklch(0.21 0.005 240);
  --ring: oklch(0.406 0.013 240);
  --primary: oklch(0.985 0.001 240);
  --primary-foreground: oklch(0.141 0.005 240);
  --secondary: oklch(0.21 0.005 240);
  --secondary-foreground: oklch(0.629 0.013 240);
  --muted: oklch(0.242 0.005 240);
  --muted-foreground: oklch(0.552 0.015 240);
  --accent: oklch(0.242 0.005 240);
  --accent-foreground: oklch(0.985 0.001 240);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.985 0 0);
  --danger: oklch(0.22 0.08 26.1);
  --danger-foreground: oklch(0.671 0.214 23.774);
  --danger-border: oklch(0.671 0.214 23.774);
  --warning: oklch(0.24 0.056 52.0);
  --warning-foreground: oklch(0.738 0.173 80.941);
  --warning-border: oklch(0.738 0.173 80.941);
  --info: oklch(0.24 0.08 268.5);
  --info-foreground: oklch(0.665 0.1895 257.22);
  --info-border: oklch(0.665 0.1895 257.22);
  --success: oklch(0.23 0.055 153.0);
  --success-foreground: oklch(0.627 0.194 149.214);
  --success-border: oklch(0.627 0.194 149.214);
  --card: oklch(0.176 0.004 240);
  --card-foreground: oklch(0.985 0.001 240);
  --popover: oklch(0.176 0.004 240);
  --popover-foreground: oklch(0.985 0.001 240);
  --chart-1: oklch(0.585 0.23 261.348);
  --chart-2: oklch(0.577 0.174 149.642);
  --chart-3: oklch(0.593 0.277 303.111);
  --chart-4: oklch(0.676 0.218 44.36);
  --chart-5: oklch(0.577 0.245 27.325);
}

/* Base styles */
*,
*::before,
*::after {
  border-color: var(--border);
}

body {
  background-color: var(--background);
  color: var(--foreground);
  font-feature-settings:
    "rlig" 1,
    "calt" 1;
}

/* Theme transitions */
:root {
  --transition-duration: 0.2s;
  --transition-easing: cubic-bezier(0.4, 0, 0.2, 1);
}

*:not(.no-transition *) {
  transition:
    background-color var(--transition-duration) var(--transition-easing),
    color var(--transition-duration) var(--transition-easing),
    border-color var(--transition-duration) var(--transition-easing),
    box-shadow var(--transition-duration) var(--transition-easing),
    opacity var(--transition-duration) var(--transition-easing);
}

/* Respect user motion preferences */
@media (prefers-reduced-motion: reduce) {
  :root {
    --transition-duration: 0.05s;
  }
}
